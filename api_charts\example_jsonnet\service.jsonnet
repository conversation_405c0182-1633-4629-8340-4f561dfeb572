{
    apiVersion: "v1",
    kind: "Service",
    metadata: {
        annotations: {
            "prometheus.io/scrape": "true"
        },
        labels: {
            app: name,
            productCode: product_code
        },
        name: name,
        namespace: namespace
    },
    spec: {
        ports: [
            {
                name: "http",
                port: 5000,
                protocol: "TCP",
                targetPort: 5000
            },
            {
                name: "metrics",
                port: 9001,
                protocol: "TCP",
                targetPort: 9001
            },
            {
                name: "netty",
                port: 5001,
                protocol: "TCP",
                targetPort: 5001
            },
            {
                name: "grpc",
                port: 5002,
                protocol: "TCP",
                targetPort: 5002
            },
        ],
        selector: {
            app: name
        },
        type: "ClusterIP"
    }
}
