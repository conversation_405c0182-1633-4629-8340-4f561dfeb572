#!/usr/bin/env python3
"""
Final validation script to ensure all Helm templates are correctly implemented
based on the example_jsonnet specifications.
"""

import yaml
import re
from pathlib import Path

def check_values_yaml():
    """Check if values.yaml has all required configurations."""
    print("🔍 Checking values.yaml configuration...")
    
    with open('api_charts/values.yaml', 'r', encoding='utf-8') as f:
        values = yaml.safe_load(f)
    
    required_keys = [
        'namespace', 'product_code', 'ingressClassName',
        'components', 'global', 'applications'
    ]
    
    missing = [key for key in required_keys if key not in values]
    if missing:
        print(f"  ❌ Missing top-level keys: {missing}")
        return False
    
    # Check components
    required_components = ['deployment_items', 'service_items', 'route', 'hpa_items']
    missing_components = [c for c in required_components if c not in values['components']]
    if missing_components:
        print(f"  ❌ Missing components: {missing_components}")
        return False
    
    # Check global configuration
    required_global = ['deployment', 'env', 'network_bit', 'ports', 'middle_ports', 'hpa']
    missing_global = [g for g in required_global if g not in values['global']]
    if missing_global:
        print(f"  ❌ Missing global config: {missing_global}")
        return False
    
    print("  ✅ values.yaml has all required configurations")
    return True

def check_template_features():
    """Check if templates implement key features from jsonnet."""
    print("\n🔍 Checking template features...")
    
    features_found = {}
    
    # Check deployment template
    with open('api_charts/templates/depolyment.yaml', 'r') as f:
        deployment_content = f.read()
    
    features_found['worker_istio_disable'] = 'sidecar.istio.io/inject' in deployment_content
    features_found['receiver_env_vars'] = 'rocketmq_log_level' in deployment_content
    features_found['middle_ports'] = 'sub_category "middle"' in deployment_content
    features_found['paramservice_delay'] = 'paramservice' in deployment_content
    features_found['topology_spread'] = 'topologySpreadConstraints' in deployment_content
    
    # Check service template
    with open('api_charts/templates/service.yaml', 'r') as f:
        service_content = f.read()
    
    features_found['service_ports'] = 'global.ports' in service_content
    features_found['middle_service_ports'] = 'global.middle_ports' in service_content
    features_found['prometheus_annotation'] = 'prometheus.io/scrape' in service_content
    
    # Check ingress template
    with open('api_charts/templates/mse_ingress.yaml', 'r') as f:
        ingress_content = f.read()
    
    features_found['grpc_ingress'] = 'roomapiservice' in ingress_content
    features_found['dynamic_hostname'] = 'network_bit' in ingress_content
    features_found['higress_backend'] = 'higress.io/backend-protocol' in ingress_content
    
    # Check HPA template
    hpa_path = Path('api_charts/templates/hpa.yaml')
    features_found['hpa_template'] = hpa_path.exists()
    if hpa_path.exists():
        with open(hpa_path, 'r') as f:
            hpa_content = f.read()
        features_found['hpa_cpu_memory'] = 'cpu' in hpa_content and 'memory' in hpa_content
    
    # Report results
    all_good = True
    feature_descriptions = {
        'worker_istio_disable': 'Worker services disable Istio sidecar',
        'receiver_env_vars': 'Receiver services get RocketMQ env vars',
        'middle_ports': 'Middle services get extra ports',
        'paramservice_delay': 'Paramservice gets probe delays',
        'topology_spread': 'Topology spread constraints',
        'service_ports': 'Service uses global port configuration',
        'middle_service_ports': 'Service includes middle ports',
        'prometheus_annotation': 'Prometheus scraping annotation',
        'grpc_ingress': 'Special gRPC ingress for roomapiservice',
        'dynamic_hostname': 'Dynamic hostname construction',
        'higress_backend': 'Higress backend protocol annotation',
        'hpa_template': 'HPA template exists',
        'hpa_cpu_memory': 'HPA supports CPU and memory metrics'
    }
    
    for feature, found in features_found.items():
        status = "✅" if found else "❌"
        print(f"  {status} {feature_descriptions[feature]}")
        if not found:
            all_good = False
    
    return all_good

def check_template_syntax():
    """Basic syntax check for template files."""
    print("\n🔍 Checking template syntax...")
    
    template_files = [
        'api_charts/templates/depolyment.yaml',
        'api_charts/templates/service.yaml',
        'api_charts/templates/mse_ingress.yaml',
        'api_charts/templates/hpa.yaml'
    ]
    
    all_good = True
    
    for template_file in template_files:
        if not Path(template_file).exists():
            print(f"  ❌ {template_file} does not exist")
            all_good = False
            continue
        
        with open(template_file, 'r') as f:
            content = f.read()
        
        # Check for balanced template blocks
        if_count = len(re.findall(r'\{\{-?\s*if\s', content))
        range_count = len(re.findall(r'\{\{-?\s*range\s', content))
        end_count = len(re.findall(r'\{\{-?\s*end\s*-?\}\}', content))
        
        expected_ends = if_count + range_count
        
        if end_count >= expected_ends:
            print(f"  ✅ {Path(template_file).name} syntax looks good")
        else:
            print(f"  ❌ {Path(template_file).name} may have unbalanced blocks")
            all_good = False
    
    return all_good

def main():
    """Main validation function."""
    print("🚀 Final Helm Template Validation")
    print("=" * 50)
    
    checks = [
        check_values_yaml(),
        check_template_features(),
        check_template_syntax()
    ]
    
    print("\n" + "=" * 50)
    
    if all(checks):
        print("🎉 ALL CHECKS PASSED!")
        print("\n📋 Summary of implemented features:")
        print("✅ Deployment template with conditional logic based on sub_category")
        print("✅ Service template with dynamic port configuration")
        print("✅ Ingress template with special routing for gRPC services")
        print("✅ HPA template for auto-scaling")
        print("✅ Complete values.yaml with all required parameters")
        print("\n🔧 Ready for Helm deployment!")
        print("Run: helm template test-release ./api_charts")
        return True
    else:
        print("❌ Some checks failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    main()
