# ===================================================================
# Top-Level & Component Controls
# ===================================================================
namespace: default
product_code: jd-api
ingressClassName: "higress"

components:
  deployment_items: true
  # ... other components

# ===================================================================
# Global Defaults: The "Blueprint"
# ===================================================================
# Values here are used unless overridden in the 'applications' list.
global:
  # --- Global Deployment Settings based on Jsonnet ---
  deployment:
    minReadySeconds: 5
    progressDeadlineSeconds: 60
    revisionHistoryLimit: 5
    terminationGracePeriodSeconds: 5
    strategy:
      type: "Recreate"
    serviceAccountName: "default" # Default service account, can be overridden
    topologySpread:
      enabled: true # Master switch for topology spread constraints

    # --- Default resource requests and limits ---
    resources:
      limits:
        cpu: "4000m"
        memory: "4Gi"
        ephemeral-storage: "50Mi"
      requests:
        cpu: "50m"
        memory: "50Mi"
        ephemeral-storage: "50Mi"

  # --- Global Environment Variables ---
  env:
    REGSERVER: "your-registry-server-ip" # e.g., Nacos, Consul IP
    METASERVER: ""
    CLUSTER: "default-cluster"

  # --- Global Volume Definitions ---
  # These are the standard volumes attached to every pod.
  volumes:
    - name: temp-volume
      emptyDir: {}
    - name: nfs-storage # Renamed for clarity
      persistentVolumeClaim:
        claimName: nfs
    - name: writeable-volume
      emptyDir: {}
    # The configuration volume is dynamically named, so it's handled in the template.

# ===================================================================
# Application List: The "Service Catalog"
# ===================================================================
applications:
  # --- Example 1: A "middle" service with http, netty, and grpc ports ---
  - name: jd-apigateway
    image: harbor.dev.wiqun.com:8443/jd/jd-apigateway:1.0.0
    sub_category: "middle" # This controls which ports are added.
    deployment:
      replicas: 2
      # Override resources if needed
      resources:
        limits: 
          cpu: "2000m"
          memory: "2Gi"
        requests: 
          cpu: "100m"
          memory: "100Mi"
    ingress:
      enabled: true # 为这个应用创建 Ingress
      domain: api.dev.wiqun.com
    service:
      enabled: true # 为这个应用创建 Service

  # --- Example 2: A "receiver" service with special env vars ---
  - name: jd-aireceiver
    image: harbor.dev.wiqun.com:8443/jd/jd.aireceiver:6.14.0
    sub_category: "receiver" # This adds RocketMQ env vars.
    

  # --- Example 3: A "worker" with Istio injection disabled ---
  - name: jd-aiworker
    image: harbor.dev.wiqun.com:8443/jd/jd.aiworker:6.14.0
    sub_category: "worker" # Disables Istio sidecar and adds KEEP_RUN_TIME env.

  # --- Example 4: The special "paramservice" with delayed probes ---
  - name: jd-paramservice # Name matches the condition <product_code>-paramservice
    image: harbor.dev.wiqun.com:8443/jd/jd-paramservice:1.1.0
    # No sub_category needed if it's a standard web service.
    # The template will automatically apply the probe delay based on the name.
