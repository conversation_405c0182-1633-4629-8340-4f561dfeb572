{{- /*
================================================================================
Template for Kubernetes Horizontal Pod Autoscaler (HPA)
================================================================================
This template generates HPA resources for applications where hpa.enabled is true.
It supports both CPU and memory-based scaling with configurable thresholds.

Key Features:
- Global switch 'components.hpa_items' controls creation of all HPAs
- Per-application 'hpa.enabled' flag for individual control
- Configurable min/max replicas and target utilization percentages
- Uses global defaults with per-application overrides
- Targets the Deployment created by the deployment template
*/}}
{{- if .Values.components.hpa_items }}
{{- range .Values.applications }}
{{- if .hpa.enabled }}
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ .name }}
  namespace: {{ $.Values.namespace }}
  labels:
    app: {{ .name }}
    productCode: {{ $.Values.product_code }}
    app.kubernetes.io/name: {{ .name }}
    app.kubernetes.io/instance: {{ $.Release.Name }}
    app.kubernetes.io/managed-by: {{ $.Release.Service }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ .name }}
  minReplicas: {{ .hpa.minReplicas | default $.Values.global.hpa.minReplicas }}
  maxReplicas: {{ .hpa.maxReplicas | default $.Values.global.hpa.maxReplicas }}
  metrics:
    {{- if .hpa.targetCPUUtilizationPercentage | default $.Values.global.hpa.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .hpa.targetCPUUtilizationPercentage | default $.Values.global.hpa.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if .hpa.targetMemoryUtilizationPercentage | default $.Values.global.hpa.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ .hpa.targetMemoryUtilizationPercentage | default $.Values.global.hpa.targetMemoryUtilizationPercentage }}
    {{- end }}
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 0
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
      - type: Pods
        value: 4
        periodSeconds: 15
      selectPolicy: Max
{{- end }}
{{- end }}
{{- end }}
