if name == product_code + "-roomapiservice" then
{
    apiVersion: "networking.k8s.io/v1",
    kind: "Ingress",
    metadata: {
        annotations: {
            "higress.io/backend-protocol": "GRPC"
        },
        labels: {
            productCode: product_code
        },
        name: name + "-grpc",
        namespace: namespace
    },
    spec: {
        ingressClassName: "higress",
        rules: [
            {
                host: "5002." + network_bit + ".chat-grpc." + product_code + "-lan.wiqun.com",
                http: {
                    paths: [
                        {
                            backend: {
                                service: {
                                    name: name,
                                    port: {
                                        number: 5002
                                    }
                                }
                            },
                            pathType: "Prefix",
                            path: "/TL.Room.Contract.RoomsContract"
                        },
                        {
                            backend: {
                                service: {
                                    name: name,
                                    port: {
                                        number: 5002
                                    }
                                }
                            },
                            pathType: "Prefix",
                            path: "/TL.Room.Contract.RoomKeysContract"
                        },
                        {
                            backend: {
                                service: {
                                    name: name,
                                    port: {
                                        number: 5002
                                    }
                                }
                            },
                            pathType: "Prefix",
                            path: "/TL.Room.Contract.RoomQueriesContract"
                        },
                        {
                            backend: {
                                service: {
                                    name: name,
                                    port: {
                                        number: 5002
                                    }
                                }
                            },
                            pathType: "Prefix",
                            path: "/TL.Room.Contract.RoomQueueContract"
                        },
                        {
                            backend: {
                                service: {
                                    name: name,
                                    port: {
                                        number: 5002
                                    }
                                }
                            },
                            pathType: "Prefix",
                            path: "/TL.Room.Contract.RoomStatisticsContract"
                        },
                        {
                            backend: {
                                service: {
                                    name: name,
                                    port: {
                                        number: 5002
                                    }
                                }
                            },
                            pathType: "Prefix",
                            path: "/TL.Room.Contract.RoomChatNodesContract"
                        },
                    ]
                }
            }
        ]
    }
}
else
{
    apiVersion: "networking.k8s.io/v1",
    kind: "Ingress",
    metadata: {
        labels: {
            productCode: product_code
        },
        name: name,
        namespace: namespace
    },
    spec: {
        ingressClassName: "higress",
        rules: [
            {
                host: port + "." + network_bit + ".api." + product_code + "-lan.wiqun.com",
                http: {
                    paths: [
                        {
                            backend: {
                                service: {
                                    name: name,
                                    port: {
                                        number: 5000
                                    }
                                }
                            },
                            pathType: "Prefix",
                            path: "/"
                        }
                    ]
                }
            }
        ]
    }
}
