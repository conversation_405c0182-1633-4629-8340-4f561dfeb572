#!/usr/bin/env python3
"""
Simple validation script to check YAML syntax and basic Helm template structure.
This script validates the templates without requiring <PERSON><PERSON> to be installed.
"""

import yaml
import os
import re
from pathlib import Path


def validate_yaml_syntax(file_path):
    """Validate YAML syntax by attempting to parse the file."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # For template files, skip YAML validation as they contain Helm directives
        if "templates" in str(file_path):
            print(f"✓ {file_path} is a Helm template (skipping YAML validation)")
            return True

        # For values.yaml, validate as pure YAML
        try:
            yaml.safe_load(content)
            print(f"✓ {file_path} has valid YAML syntax")
            return True
        except yaml.YAMLError as e:
            print(f"✗ {file_path} has YAML syntax error: {e}")
            return False

    except Exception as e:
        print(f"✗ Error reading {file_path}: {e}")
        return False


def check_template_structure(file_path):
    """Check for common Helm template patterns and potential issues."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        issues = []

        # Check for common template issues
        if "{{- if" in content and "{{- end }}" not in content:
            issues.append("Missing {{- end }} for conditional blocks")

        if "{{- range" in content and "{{- end }}" not in content:
            issues.append("Missing {{- end }} for range blocks")

        # Check for proper indentation in template blocks
        lines = content.split("\n")
        for i, line in enumerate(lines, 1):
            if "{{-" in line and line.strip().startswith("{{-"):
                # Template directive should be properly indented
                pass

        if issues:
            print(f"⚠ Template structure issues in {file_path}:")
            for issue in issues:
                print(f"  - {issue}")
            return False
        else:
            print(f"✓ {file_path} has good template structure")
            return True

    except Exception as e:
        print(f"✗ Error checking template structure in {file_path}: {e}")
        return False


def main():
    """Main validation function."""
    print("Validating Helm templates...")
    print("=" * 50)

    # Define paths to validate
    template_dir = Path("api_charts/templates")
    values_file = Path("api_charts/values.yaml")

    all_valid = True

    # Validate values.yaml
    if values_file.exists():
        print(f"\nValidating {values_file}...")
        if not validate_yaml_syntax(values_file):
            all_valid = False
    else:
        print(f"✗ {values_file} not found")
        all_valid = False

    # Validate template files
    if template_dir.exists():
        template_files = list(template_dir.glob("*.yaml"))

        for template_file in template_files:
            print(f"\nValidating {template_file}...")
            if not validate_yaml_syntax(template_file):
                all_valid = False
            if not check_template_structure(template_file):
                all_valid = False
    else:
        print(f"✗ Template directory {template_dir} not found")
        all_valid = False

    print("\n" + "=" * 50)
    if all_valid:
        print("✓ All templates passed validation!")
        print("\nNext steps:")
        print("1. Install Helm if not already installed")
        print("2. Run: helm template test-release ./api_charts")
        print("3. Run: helm lint ./api_charts")
    else:
        print("✗ Some templates have issues that need to be fixed")

    return all_valid


if __name__ == "__main__":
    main()
