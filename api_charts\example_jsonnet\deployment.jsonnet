{
    apiVersion: "apps/v1",
    kind: "Deployment",
    metadata: {
        labels: {
            app: name,
            owner: namespace,
            productCode: product_code,
        },
        name: name,
        namespace: namespace
    },
    spec: {
        minReadySeconds: 5,
        progressDeadlineSeconds: 60,
        revisionHistoryLimit: 5,
        replicas: 1,
        selector: {
            matchLabels: {
                app: name
            }
        },
        strategy: {
            type: "Recreate"
        },
        template: {
            metadata: {
                annotations: if sub_category == "worker" then { "sidecar.istio.io/inject": "false" } else {},
                labels: {
                    app: name,
                    productCode: product_code
                }
            },
            spec: {
                containers: [
                    {
                        env: [
                            {
                                name: "PRODUCTCODE",
                                value: product_code_upper
                            },
                            {
                                name: "REGSERVER",
                                value: ip
                            },
                            {
                                name: "METASERVER",
                                value: ""
                            },
                            {
                                name: "CLUSTER",
                                value: ""
                            },
                            {
                                name: "OTEL_SERVICE_NAME",
                                value: namespace + "-" + name
                            }
                        ] + ( if sub_category == "receiver" then
                                [
                                    {
                                        name: "rocketmq_log_level",
                                        value: "DEBUG"
                                    },
                                    {
                                        name: "mq_consoleAppender_enabled",
                                        value: "true"
                                    }
                                ] else []
                        ) + ( if sub_category == "worker" then
                            [
                                {
                                    name: "KEEP_RUN_TIME",
                                    value: "311040000"
                                }
                            ] else []
                        ),
                        image: repo + ":" + tag,
                        imagePullPolicy: "IfNotPresent",
                        livenessProbe: {
                            failureThreshold: 3,
                            httpGet: {
                                httpHeaders: [
                                    {
                                        name: "X-Kubernetes-Header",
                                        value: "HealthCheck"
                                    }
                                ],
                                path: "/base/basehealthes/heartbeat",
                                port: "http",
                                scheme: "HTTP"
                            },
                            initialDelaySeconds: if name == product_code + "-paramservice" then 20 else 5,
                            periodSeconds: 12,
                            successThreshold: 1,
                            timeoutSeconds: 12
                        },
                        name: name,
                        ports: [
                            {
                                containerPort: 5000,
                                name: "http",
                                protocol: "TCP"
                            },
                        ] + ( if sub_category == "middle" then
                            [
                                {
                                    containerPort: 5001,
                                    name: "netty",
                                    protocol: "TCP"
                                },
                                {
                                    containerPort: 5002,
                                    name: "grpc",
                                    protocol: "TCP"
                                }
                            ] else []
                        ),
                        readinessProbe: {
                            failureThreshold: 4,
                            httpGet: {
                                httpHeaders: [
                                    {
                                        name: "X-Kubernetes-Header",
                                        value: "ReadCheck"
                                    }
                                ],
                                path: "/base/basehealthes/readiness",
                                port: "http",
                                scheme: "HTTP"
                            },
                            initialDelaySeconds: if name == product_code + "-paramservice" then 20 else 5,
                            periodSeconds: 6,
                            timeoutSeconds: 6
                        },
                        resources: {
                            limits: {
                                cpu: "4000m",
                                "ephemeral-storage": "50Mi",
                                memory: "4Gi"
                            },
                            requests: {
                                cpu: "50m",
                                "ephemeral-storage": "50Mi",
                                memory: "50Mi"
                            }
                        },
                        securityContext: {
                            allowPrivilegeEscalation: false,
                            capabilities: {
                                drop: [
                                    "ALL"
                                ]
                            },
                            readOnlyRootFilesystem: true
                        },
                        startupProbe: {
                            failureThreshold: 6,
                            httpGet: {
                                port: "http",
                                path: "/base/basehealthes/heartbeat"
                            },
                            periodSeconds: 10
                        },
                        volumeMounts: [
                            {
                                mountPath: "/tmp",
                                name: "temp-volume"
                            },
                            {
                                mountPath: "/app/" + product_code,
                                name: product_code + "-dev-configuration"
                            },
                            {
                                mountPath: "/mnt",
                                name: "nfs"
                            },
                            {
                                mountPath: "/writeable",
                                name: "writeable-volume",
                                readOnly: false
                            }
                        ]
                    }
                ],
                serviceAccountName: service_account_name,
                terminationGracePeriodSeconds: 5,
                topologySpreadConstraints: [
                    {
                        labelSelector: {
                            matchLabels: {
                                app: name
                            }
                        },
                        maxSkew: 1,
                        topologyKey: "kubernetes.io/hostname",
                        whenUnsatisfiable: "ScheduleAnyway"
                    }
                ],
                volumes: [
                    {
                        emptyDir: {},
                        name: "temp-volume"
                    },
                    {
                        name: product_code + "-dev-configuration",
                        persistentVolumeClaim: {
                            claimName: product_code + "-dev-configuration"
                        }
                    },
                    {
                        name: "nfs",
                        persistentVolumeClaim: {
                            claimName: "nfs"
                        }
                    },
                    {
                        emptyDir: {},
                        name: "writeable-volume"
                    }
                ]
            }
        }
    }
}
