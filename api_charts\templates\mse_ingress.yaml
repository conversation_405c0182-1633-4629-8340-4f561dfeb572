{{- /*
================================================================================
Template for Kubernetes Ingress (v2 - based on conditional Jsonnet)
================================================================================
This template generates a Kubernetes Ingress resource for each application
in `.Values.applications` where `ingress.enabled` is true.

It implements complex conditional logic based on the application's name,
replicating the behavior of the provided Jsonnet template.

Key Features:
- A global switch 'components.route' controls the creation of all Ingresses.
- Special Case Logic: If an application's name matches '<product_code>-roomapiservice',
  it generates a specific multi-path gRPC Ingress.
- Default Case Logic: For all other applications, it generates a standard
  HTTP Ingress with a root path.
- Hostnames are dynamically constructed according to the Jsonnet rules,
  using a new 'global.network_bit' value.
- The 'ingress.domain' field from the values is intentionally ignored to
  match the Jsonnet logic.
*/}}
{{- /* Master switch for creating any Ingress resources. */}}
{{- if .Values.components.route }}
{{- range .Values.applications }}
{{- if .ingress.enabled }}
---
{{- /* Construct the special name to check against. e.g., "jd-roomapiservice" */}}
{{- $specialGrpcServiceName := printf "%s-roomapiservice" $.Values.product_code }}

{{- if eq .name $specialGrpcServiceName }}
#
# SPECIAL CASE: Generating gRPC Ingress for '{{ .name }}'
#
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .name }}-grpc
  namespace: {{ $.Values.namespace }}
  annotations:
    # Annotation required by Higress for gRPC routing
    "higress.io/backend-protocol": "GRPC"
  labels:
    productCode: {{ $.Values.product_code }}
    app.kubernetes.io/name: {{ .name }}
    app.kubernetes.io/instance: {{ $.Release.Name }}
spec:
  ingressClassName: {{ $.Values.ingressClassName }}
  rules:
    - host: "5002.{{ $.Values.global.network_bit }}.chat-grpc.{{ $.Values.product_code }}-lan.wiqun.com"
      http:
        paths:
          {{- /* List of all required gRPC service paths */}}
          - path: /TL.Room.Contract.RoomsContract
            pathType: Prefix
            backend:
              service:
                name: {{ .name }}
                port:
                  number: 5002
          - path: /TL.Room.Contract.RoomKeysContract
            pathType: Prefix
            backend:
              service:
                name: {{ .name }}
                port:
                  number: 5002
          - path: /TL.Room.Contract.RoomQueriesContract
            pathType: Prefix
            backend:
              service:
                name: {{ .name }}
                port:
                  number: 5002
          - path: /TL.Room.Contract.RoomQueueContract
            pathType: Prefix
            backend:
              service:
                name: {{ .name }}
                port:
                  number: 5002
          - path: /TL.Room.Contract.RoomStatisticsContract
            pathType: Prefix
            backend:
              service:
                name: {{ .name }}
                port:
                  number: 5002
          - path: /TL.Room.Contract.RoomChatNodesContract
            pathType: Prefix
            backend:
              service:
                name: {{ .name }}
                port:
                  number: 5002

{{- else }}
#
# DEFAULT CASE: Generating standard HTTP Ingress for '{{ .name }}'
#
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ .name }}
  namespace: {{ $.Values.namespace }}
  labels:
    productCode: {{ $.Values.product_code }}
    app.kubernetes.io/name: {{ .name }}
    app.kubernetes.io/instance: {{ $.Release.Name }}
spec:
  ingressClassName: {{ $.Values.ingressClassName }}
  rules:
    - host: "5000.{{ $.Values.global.network_bit }}.api.{{ $.Values.product_code }}-lan.wiqun.com"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: {{ .name }}
                port:
                  number: 5000
{{- end }}{{/* End of if/else block */}}
{{- end }}{{/* End of if .ingress.enabled */}}
{{- end }}{{/* End of range */}}
{{- end }}{{/* End of if .Values.components.route */}}