{{- /*
================================================================================
Template for Kubernetes Deployments (v3 - based on complex Jsonnet)
================================================================================
This template generates Deployments based on a complex Jsonnet specification.
It uses an application's 'sub_category' (e.g., worker, receiver, middle)
and name to conditionally apply settings.

Key Logic Implemented:
- Conditional Istio sidecar injection based on `sub_category: worker`.
- Conditional environment variables for `receiver` and `worker` types.
- Conditional ports for `middle` services (adds netty, grpc).
- Conditional probe delays for a specific service name (`<product_code>-paramservice`).
- Uses a "global default + local override" pattern for replicas, resources, and more.
- Labels and selectors are precisely matched to the Jsonnet specification (`app: name`).
*/}}
{{- if .Values.components.deployment_items }}
{{- range .Values.applications }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .name }}
  namespace: {{ $.Values.namespace }}
  labels:
    app: {{ .name }}
    owner: {{ $.Values.namespace }}
    productCode: {{ $.Values.product_code }}
spec:
  replicas: {{ .deployment.replicas | default 1 }}
  selector:
    matchLabels:
      app: {{ .name }}
  strategy:
    {{- toYaml $.Values.global.deployment.strategy | nindent 4 }}
  minReadySeconds: {{ $.Values.global.deployment.minReadySeconds }}
  progressDeadlineSeconds: {{ $.Values.global.deployment.progressDeadlineSeconds }}
  revisionHistoryLimit: {{ $.Values.global.deployment.revisionHistoryLimit }}
  template:
    metadata:
      {{- if eq .sub_category "worker" }}
      annotations:
        # Disable Istio sidecar injection for worker-type pods
        "sidecar.istio.io/inject": "false"
      {{- end }}
      labels:
        app: {{ .name }}
        productCode: {{ $.Values.product_code }}
    spec:
      serviceAccountName: {{ .deployment.serviceAccountName | default $.Values.global.deployment.serviceAccountName }}
      terminationGracePeriodSeconds: {{ $.Values.global.deployment.terminationGracePeriodSeconds }}
      {{- if $.Values.global.deployment.topologySpread.enabled }}
      topologySpreadConstraints:
        - labelSelector:
            matchLabels:
              app: {{ .name }}
          maxSkew: 1
          topologyKey: "kubernetes.io/hostname"
          whenUnsatisfiable: "ScheduleAnyway"
      {{- end }}
      containers:
        - name: {{ .name }}
          image: "{{ .image }}"
          imagePullPolicy: "IfNotPresent"
          env:
            - name: "PRODUCTCODE"
              value: {{ $.Values.product_code | upper }}
            - name: "REGSERVER"
              value: {{ $.Values.global.env.REGSERVER | quote }}
            - name: "METASERVER"
              value: {{ $.Values.global.env.METASERVER | quote }}
            - name: "CLUSTER"
              value: {{ $.Values.global.env.CLUSTER | quote }}
            - name: "OTEL_SERVICE_NAME"
              value: "{{ $.Values.namespace }}-{{ .name }}"
            {{- /* Add environment variables for 'receiver' sub-category */}}
            {{- if eq .sub_category "receiver" }}
            - name: "rocketmq_log_level"
              value: "DEBUG"
            - name: "mq_consoleAppender_enabled"
              value: "true"
            {{- end }}
            {{- /* Add environment variables for 'worker' sub-category */}}
            {{- if eq .sub_category "worker" }}
            - name: "KEEP_RUN_TIME"
              value: "311040000"
            {{- end }}

          ports:
            - name: "http"
              containerPort: 5000
              protocol: "TCP"
            - name: "metrics"
              containerPort: 9001
              protocol: "TCP"
            {{- /* Add extra ports for 'middle' sub-category */}}
            {{- if eq .sub_category "middle" }}
            - name: "netty"
              containerPort: 5001
              protocol: "TCP"
            - name: "grpc"
              containerPort: 5002
              protocol: "TCP"
            {{- end }}

          {{- /* Define a variable for the special service name to simplify the if condition */}}
          {{- $paramService := printf "%s-paramservice" $.Values.product_code }}
          livenessProbe:
            httpGet:
              path: "/base/basehealthes/heartbeat"
              port: "http"
              scheme: "HTTP"
              httpHeaders:
                - name: "X-Kubernetes-Header"
                  value: "HealthCheck"
            initialDelaySeconds: {{ if eq .name $paramService }}20{{ else }}5{{ end }}
            periodSeconds: 12
            timeoutSeconds: 12
            successThreshold: 1
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: "/base/basehealthes/readiness"
              port: "http"
              scheme: "HTTP"
              httpHeaders:
                - name: "X-Kubernetes-Header"
                  value: "ReadCheck"
            initialDelaySeconds: {{ if eq .name $paramService }}20{{ else }}5{{ end }}
            periodSeconds: 6
            timeoutSeconds: 6
            successThreshold: 1
            failureThreshold: 4
          startupProbe:
            httpGet:
              path: "/base/basehealthes/heartbeat"
              port: "http"
            periodSeconds: 10
            failureThreshold: 6

          resources:
            {{- /* Use application-specific resources if defined, otherwise use global ones */}}
            {{- $resources := .deployment.resources | default $.Values.global.deployment.resources }}
            {{- toYaml $resources | nindent 12 }}

          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop: ["ALL"]

          volumeMounts:
            - name: temp-volume
              mountPath: /tmp
            - name: {{ $.Values.product_code }}-dev-configuration
              mountPath: /app/{{ $.Values.product_code }}
            - name: nfs-storage
              mountPath: /mnt
            - name: writeable-volume
              mountPath: /writeable
              readOnly: false

      volumes:
        - name: {{ $.Values.product_code }}-dev-configuration
          persistentVolumeClaim:
            claimName: {{ $.Values.product_code }}-dev-configuration
        {{- /* Render volumes from the global list */}}
        {{- toYaml $.Values.global.volumes | nindent 8 }}
{{- end }}
{{- end }}
{{- end }}