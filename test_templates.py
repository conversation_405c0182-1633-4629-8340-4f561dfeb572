#!/usr/bin/env python3
"""
Simple test script to check if our Helm templates have the correct structure
and reference the right values from values.yaml.
"""

import yaml
import re
from pathlib import Path

def load_values():
    """Load values.yaml and return as dict."""
    with open('api_charts/values.yaml', 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)

def check_template_references(template_path, values):
    """Check if template references valid values."""
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"\nChecking {template_path}...")
    
    # Find all template references
    references = re.findall(r'\{\{\s*[^}]+\s*\}\}', content)
    
    issues = []
    
    for ref in references:
        # Clean up the reference
        clean_ref = ref.strip('{}').strip()
        
        # Skip complex expressions and functions
        if any(keyword in clean_ref for keyword in ['if', 'range', 'end', 'toYaml', 'nindent', 'default', 'printf', 'eq']):
            continue
        
        # Check simple value references
        if clean_ref.startswith('.Values.'):
            # Extract the path
            path = clean_ref[8:]  # Remove '.Values.'
            
            # Check if path exists in values
            try:
                current = values
                for part in path.split('.'):
                    if part.startswith('$'):
                        continue  # Skip $ references
                    current = current[part]
            except (KeyError, TypeError):
                issues.append(f"Reference '{clean_ref}' not found in values.yaml")
    
    if issues:
        print(f"  ⚠ Issues found:")
        for issue in issues:
            print(f"    - {issue}")
        return False
    else:
        print(f"  ✓ All references look valid")
        return True

def check_template_syntax(template_path):
    """Basic syntax check for Helm templates."""
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    issues = []
    
    # Check for balanced template blocks
    if_count = len(re.findall(r'\{\{-?\s*if\s', content))
    end_count = len(re.findall(r'\{\{-?\s*end\s*-?\}\}', content))
    
    range_count = len(re.findall(r'\{\{-?\s*range\s', content))
    
    # Each if and range should have a corresponding end
    expected_ends = if_count + range_count
    
    if end_count < expected_ends:
        issues.append(f"Missing {{- end }} blocks. Found {end_count}, expected at least {expected_ends}")
    
    # Check for common syntax issues
    if '{{-' in content and '-}}' not in content:
        issues.append("Found {{- without corresponding -}}")
    
    return issues

def main():
    """Main test function."""
    print("Testing Helm templates...")
    print("=" * 50)
    
    # Load values
    try:
        values = load_values()
        print("✓ values.yaml loaded successfully")
    except Exception as e:
        print(f"✗ Error loading values.yaml: {e}")
        return False
    
    # Test template files
    template_dir = Path("api_charts/templates")
    template_files = list(template_dir.glob("*.yaml"))
    
    all_good = True
    
    for template_file in template_files:
        # Check syntax
        syntax_issues = check_template_syntax(template_file)
        if syntax_issues:
            print(f"\n✗ Syntax issues in {template_file}:")
            for issue in syntax_issues:
                print(f"  - {issue}")
            all_good = False
        else:
            print(f"\n✓ {template_file} syntax looks good")
        
        # Check references
        if not check_template_references(template_file, values):
            all_good = False
    
    print("\n" + "=" * 50)
    if all_good:
        print("✓ All templates passed basic validation!")
        print("\nKey features implemented:")
        print("- ✓ Deployment template with conditional logic")
        print("- ✓ Service template with port configuration")
        print("- ✓ Ingress template with conditional routing")
        print("- ✓ HPA template with scaling configuration")
        print("- ✓ Values.yaml with all required parameters")
        print("\nTo test with Helm:")
        print("1. Install Helm: https://helm.sh/docs/intro/install/")
        print("2. Run: helm template test-release ./api_charts")
        print("3. Run: helm lint ./api_charts")
    else:
        print("✗ Some templates have issues that need to be fixed")
    
    return all_good

if __name__ == "__main__":
    main()
