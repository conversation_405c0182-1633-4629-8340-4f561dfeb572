{{- /*
================================================================================
Template for Kubernetes Services (based on Jsonnet specification)
================================================================================
This template generates Service resources for each application where service.enabled is true.
It includes standard ports for all services and additional ports for "middle" category services.

Key Features:
- Standard ports (http, metrics) for all services
- Additional ports (netty, grpc) for "middle" sub-category services
- Labels and selectors match the Deployment template
- Prometheus scraping annotation enabled
*/}}
{{- if .Values.components.service_items }}
{{- range .Values.applications }}
{{- if .service.enabled }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .name }}
  namespace: {{ $.Values.namespace }}
  annotations:
    # This annotation enables Prometheus scraping as per Jsonnet reference
    "prometheus.io/scrape": "true"
  labels:
    # Labels as defined in the Jsonnet reference
    app: {{ .name }}
    productCode: {{ $.Values.product_code }}
spec:
  type: ClusterIP
  ports:
    {{- /* Standard ports for all services */}}
    {{- range $.Values.global.ports }}
    - name: {{ .name }}
      port: {{ .containerPort }}
      targetPort: {{ .containerPort }}
      protocol: {{ .protocol }}
    {{- end }}
    {{- /* Additional ports for "middle" category services */}}
    {{- if eq .sub_category "middle" }}
    {{- range $.Values.global.middle_ports }}
    - name: {{ .name }}
      port: {{ .containerPort }}
      targetPort: {{ .containerPort }}
      protocol: {{ .protocol }}
    {{- end }}
    {{- end }}
  selector:
    # This selector must match the labels on the Pods managed by the Deployment
    app: {{ .name }}
{{- end }}
{{- end }}
{{- end }}