{{- if .Values.components.service_items }}
{{- range .Values.applications }}
{{- if .service.enabled }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ .name }}
  namespace: {{ $.Values.namespace }}
  annotations:
    # This annotation is included from your Jsonnet reference to enable Prometheus scraping
    prometheus.io/scrape: "true"
  labels:
    # Labels as defined in the Jsonnet reference
    app: {{ .name }}
    product_code: {{ $.Values.product_code }}
    # Including standard Kubernetes/Helm labels for best practice
    app.kubernetes.io/name: {{ .name }}
    app.kubernetes.io/instance: {{ $.Release.Name }}
    app.kubernetes.io/managed-by: {{ $.Release.Service }}
spec:
  type: ClusterIP
  ports:
    {{- range $.Values.global.ports }}
    - name: {{ .name }}
      port: {{ .containerPort }}
      targetPort: {{ .name }}
      protocol: {{ .protocol }}
    {{- end }}
    {{- range $.Values.global.middle_ports }}
    - name: {{ .name }}
      port: {{ .containerPort }}
      targetPort: {{ .name }}
      protocol: {{ .protocol }}
    {{- end }}
  selector:
    # This selector must match the labels on the Pods managed by the Deployment.
    # We use the same labels as the Deployment template for consistency and correctness.
    app.kubernetes.io/name: {{ .name }}
    product_code: {{ $.Values.product_code }}
{{- end }}
{{- end }}
{{- end }}