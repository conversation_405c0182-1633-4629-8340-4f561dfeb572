**使用方法：**

1. 确保有一个可以使用的 k8s 集群以及 helm
2. 克隆仓库文件夹
3. 克隆完可能会出现 Zone.Identifier 流，这是 windows 来标记这个文件的来源，以便在将来打开文件时，提醒用户此文件可能不安全。需要删除后才可正常运行
4. 直接运行`helm install api /path/to/charts`即可部署
5. 运行`helm status api`可查看部署情况
6. 卸载运行`helm uninstall api`

**常用命令**

```
# 创建一个charts 仓库
helm create api

# 将模板部署到集群
helm install api /path/to/floder

# 获取当前模板内容
helm get manifest api 获取模板内容

# 将模板部署的资源从集群卸载
helm uninstall api

# 运行上传 但不真正执行 可用于查看渲染后的内容
helm install api /path/to/floder --dry-run --debug

# 可用于查看渲染后的内容
helm template api /path/to/charts --values /path/to/values.yaml --debug
```

**当前参数**

查看`values.yaml`
